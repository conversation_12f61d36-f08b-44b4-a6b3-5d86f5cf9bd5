# Copilot Instructions for Chatbot Admin

## Project Overview
This is a **chatbot administration system** built as a yarn workspace monorepo with an Express.js backend and a React TypeScript frontend (now using Vite instead of Create React App/webpack). The project is in early development with basic scaffolding in place.

## Architecture & Structure
```
chatbot_admin/                 # Root workspace
├── backend/                   # Express.js API server
│   ├── app.js                # Main Express app
│   ├── bin/www               # Server startup script
│   ├── routes/               # Express route handlers
│   └── prisma/schema.prisma  # Database schema (PostgreSQL)
└── frontend/                 # React TypeScript SPA (Vite-based)
    ├── vite.config.ts        # Vite configuration
    └── src/                  # React components and logic
```

## Key Development Workflows

### Starting Development
- **Full stack**: `yarn dev` from root (runs both backend and frontend concurrently)
- **Backend only**: `yarn --cwd backend start` (runs on port 3000)
- **Frontend only**: `yarn --cwd frontend dev` (runs on port 5173 by default with Vite)

### Database Management
- Prisma schema is in `backend/prisma/schema.prisma`
- Prisma client generates to `backend/generated/prisma/` (non-standard location)
- Uses PostgreSQL (requires `DATABASE_URL` environment variable)

## Project-Specific Patterns

### Backend (Express.js)
- Traditional Express structure with separate route files in `routes/`
- No middleware setup beyond basics (morgan, cookie-parser, cors)
- Prisma client integration not yet implemented in routes
- Static file serving from `backend/public/`

### Frontend (React + TypeScript)
### Frontend (React + TypeScript, Vite)
- Vite-based setup with TypeScript (`frontend/`)
- **React Router v6** for client-side routing with nested routes
- **Ant Design v6.0.0-alpha.1** for UI components with React 19 compatibility
- Standard Vite file structure: entry in `frontend/src/`, config in `frontend/vite.config.ts`
- Page components in `frontend/src/pages/` (Dashboard, Chatbots, Conversations, Settings)
- Layout component in `frontend/src/components/AppLayout.tsx` with sidebar navigation
- Strict TypeScript configuration enabled

### Workspace Management
- Uses yarn workspaces for dependency management
- `concurrently` package handles parallel development servers
- No shared packages or utilities between frontend/backend yet

## Development Guidelines

### Database Schema Changes
1. Modify `backend/prisma/schema.prisma`
2. Run `npx prisma migrate dev` from backend directory
3. Generated client will be in `backend/generated/prisma/`

### API Development
- Add new routes in `backend/routes/` following existing pattern
- Import and register routes in `backend/app.js`
- Use Prisma client for database operations (when implemented)

### Frontend Development
- Components go in `frontend/src/` with pages in `frontend/src/pages/`
- Use React Router v6 for navigation: routes defined in `App.tsx`, layout in `AppLayout.tsx`
- Use Vite commands: `yarn --cwd frontend dev` (development), `yarn --cwd frontend build` (production build), `yarn --cwd frontend preview` (preview build)
- Follow React functional component patterns with TypeScript
- Ant Design components for consistent UI/UX
- TypeScript strict mode is enabled

### Cross-Service Communication
- Backend API typically runs on port 3000
- Frontend (Vite) runs on port 5173 by default; configure Vite's `proxy` option in `vite.config.ts` for API calls to backend
- No API integration implemented yet between frontend and backend

## Missing Infrastructure (Early Stage)
- Environment variable configuration (`.env` files)
- API route implementations beyond basic scaffolding
- Frontend-backend integration
- Authentication/authorization
- Database models and relationships
- Build and deployment scripts
- Testing setup beyond CRA defaults
