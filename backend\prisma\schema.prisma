// schema.prisma

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Product {
  id           Int           @id @default(autoincrement())
  name         String
  price        Float
  imageUrls    String[]      // array URL ảnh sản phẩm
  ingredients  String
  instructions String        // Cách chế biến
  benefits     String        // Lợi ích
  usage        String        // Cách sử dụng
  notes        String?
  otherInfo    String?
  targetUser   String?
  purpose      String?
  faqs         ProductFAQ[]
  orderItems   OrderItem[]
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt
}

model ProductFAQ {
  id         Int      @id @default(autoincrement())
  product    Product  @relation(fields: [productId], references: [id])
  productId  Int
  question   String
  answer     String
}

model ShopPolicy {
  id       Int    @id @default(autoincrement())
  title    String
  content  String
  type     String // "shipping", "promotion", "payment", "return"...
}

model MenuImage {
  id        Int    @id @default(autoincrement())
  imageUrl  String
  title     String
  order     Int?
}

model User {
  id          Int      @id @default(autoincrement())
  fbId        String   @unique // Facebook user id
  name        String?
  avatar      String?
  lastMessage DateTime?
  orders      Order[]
}

model Order {
  id           Int        @id @default(autoincrement())
  user         User       @relation(fields: [userId], references: [id])
  userId       Int
  items        OrderItem[]
  status       String     // pending, paid, shipping, done, canceled
  totalAmount  Float
  createdAt    DateTime   @default(now())
  note         String?
}

model OrderItem {
  id         Int      @id @default(autoincrement())
  order      Order    @relation(fields: [orderId], references: [id])
  orderId    Int
  product    Product  @relation(fields: [productId], references: [id])
  productId  Int
  quantity   Int
  price      Float
}
