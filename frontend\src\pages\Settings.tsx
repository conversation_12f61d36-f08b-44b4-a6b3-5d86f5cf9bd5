import React from 'react'
import { Card, Form, Input, Switch, Button, Space, Divider, Select, Slider } from 'antd'
import { SaveOutlined, ReloadOutlined } from '@ant-design/icons'

const { TextArea } = Input
const { Option } = Select

const Settings: React.FC = () => {
  const [form] = Form.useForm()

  const onFinish = (values: any) => {
    console.log('Settings saved:', values)
  }

  const onReset = () => {
    form.resetFields()
  }

  return (
    <div>
      <h2 style={{ color: '#1890ff', marginBottom: '24px' }}>Settings</h2>

      <Form
        form={form}
        layout="vertical"
        onFinish={onFinish}
        initialValues={{
          systemName: 'Chatbot Admin System',
          adminEmail: '<EMAIL>',
          enableNotifications: true,
          enableAnalytics: true,
          autoBackup: true,
          sessionTimeout: 30,
          maxConcurrentConversations: 100,
          defaultLanguage: 'en',
          theme: 'light'
        }}
      >
        <Card title="General Settings" style={{ marginBottom: '24px' }}>
          <Form.Item
            label="System Name"
            name="systemName"
            rules={[{ required: true, message: 'Please enter system name' }]}
          >
            <Input placeholder="Enter system name" />
          </Form.Item>

          <Form.Item
            label="Admin Email"
            name="adminEmail"
            rules={[
              { required: true, message: 'Please enter admin email' },
              { type: 'email', message: 'Please enter a valid email' }
            ]}
          >
            <Input placeholder="Enter admin email" />
          </Form.Item>

          <Form.Item
            label="Default Language"
            name="defaultLanguage"
          >
            <Select placeholder="Select default language">
              <Option value="en">English</Option>
              <Option value="vi">Vietnamese</Option>
              <Option value="es">Spanish</Option>
              <Option value="fr">French</Option>
            </Select>
          </Form.Item>

          <Form.Item
            label="Theme"
            name="theme"
          >
            <Select placeholder="Select theme">
              <Option value="light">Light</Option>
              <Option value="dark">Dark</Option>
              <Option value="auto">Auto</Option>
            </Select>
          </Form.Item>
        </Card>

        <Card title="System Configuration" style={{ marginBottom: '24px' }}>
          <Form.Item
            label="Session Timeout (minutes)"
            name="sessionTimeout"
          >
            <Slider
              min={5}
              max={120}
              marks={{
                5: '5min',
                30: '30min',
                60: '1hr',
                120: '2hrs'
              }}
            />
          </Form.Item>

          <Form.Item
            label="Max Concurrent Conversations"
            name="maxConcurrentConversations"
          >
            <Slider
              min={10}
              max={500}
              step={10}
              marks={{
                10: '10',
                100: '100',
                250: '250',
                500: '500'
              }}
            />
          </Form.Item>

          <Divider />

          <Form.Item
            name="enableNotifications"
            valuePropName="checked"
          >
            <Space>
              <Switch />
              <span>Enable Email Notifications</span>
            </Space>
          </Form.Item>

          <Form.Item
            name="enableAnalytics"
            valuePropName="checked"
          >
            <Space>
              <Switch />
              <span>Enable Analytics & Reporting</span>
            </Space>
          </Form.Item>

          <Form.Item
            name="autoBackup"
            valuePropName="checked"
          >
            <Space>
              <Switch />
              <span>Enable Automatic Backup</span>
            </Space>
          </Form.Item>
        </Card>

        <Card title="API Configuration" style={{ marginBottom: '24px' }}>
          <Form.Item
            label="API Base URL"
            name="apiBaseUrl"
          >
            <Input placeholder="https://api.example.com" />
          </Form.Item>

          <Form.Item
            label="API Key"
            name="apiKey"
          >
            <Input.Password placeholder="Enter API key" />
          </Form.Item>

          <Form.Item
            label="Webhook URL"
            name="webhookUrl"
          >
            <Input placeholder="https://webhook.example.com" />
          </Form.Item>
        </Card>

        <Card title="Advanced Settings" style={{ marginBottom: '24px' }}>
          <Form.Item
            label="Custom CSS"
            name="customCss"
          >
            <TextArea
              rows={4}
              placeholder="Enter custom CSS rules..."
            />
          </Form.Item>

          <Form.Item
            label="Custom JavaScript"
            name="customJs"
          >
            <TextArea
              rows={4}
              placeholder="Enter custom JavaScript code..."
            />
          </Form.Item>
        </Card>

        <Form.Item>
          <Space>
            <Button type="primary" htmlType="submit" icon={<SaveOutlined />}>
              Save Settings
            </Button>
            <Button onClick={onReset} icon={<ReloadOutlined />}>
              Reset to Default
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </div>
  )
}

export default Settings
