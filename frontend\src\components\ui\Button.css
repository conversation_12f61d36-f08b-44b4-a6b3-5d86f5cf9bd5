/* Button component styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  text-decoration: none;
  font-family: inherit;
  outline: none;
}

/* Size variants */
.btn--small {
  padding: 8px 16px;
  font-size: 14px;
  min-height: 32px;
}

.btn--medium {
  padding: 12px 20px;
  font-size: 16px;
  min-height: 40px;
}

.btn--large {
  padding: 16px 24px;
  font-size: 18px;
  min-height: 48px;
}

/* Color variants */
.btn--primary {
  background-color: #007bff;
  color: white;
}

.btn--primary:hover:not(:disabled) {
  background-color: #0056b3;
}

.btn--secondary {
  background-color: #6c757d;
  color: white;
}

.btn--secondary:hover:not(:disabled) {
  background-color: #545b62;
}

.btn--danger {
  background-color: #dc3545;
  color: white;
}

.btn--danger:hover:not(:disabled) {
  background-color: #c82333;
}

/* Loading state */
.btn--loading {
  cursor: not-allowed;
  opacity: 0.7;
}

.btn__spinner {
  display: inline-block;
  animation: spin 1s linear infinite;
}

/* Disabled state */
.btn:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
