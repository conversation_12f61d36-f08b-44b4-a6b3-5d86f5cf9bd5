import React, { useState } from 'react';
import { 
  Typography, 
  Table, 
  Space, 
  Tag, 
  Button, 
  Input, 
  Select, 
  Card,
  Row,
  Col,
  Avatar,
  Tooltip,
  Modal,
  List,
  Divider
} from 'antd';
import { 
  SearchOutlined, 
  FilterOutlined, 
  EyeOutlined,
  UserOutlined,
  RobotOutlined,
  MessageOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';

const { Title } = Typography;
const { Search } = Input;
const { Option } = Select;

interface ConversationData {
  key: string;
  id: string;
  user: {
    id: string;
    name: string;
    avatar?: string;
  };
  chatbot: {
    id: string;
    name: string;
  };
  status: 'active' | 'completed' | 'abandoned';
  messageCount: number;
  startTime: string;
  endTime?: string;
  duration: string;
  satisfaction?: number;
}

interface Message {
  id: string;
  sender: 'user' | 'bot';
  content: string;
  timestamp: string;
}

const ConversationsPage: React.FC = () => {
  const [selectedConversation, setSelectedConversation] = useState<ConversationData | null>(null);
  const [isModalVisible, setIsModalVisible] = useState(false);

  // Mock conversation data
  const data: ConversationData[] = [
    {
      key: '1',
      id: 'conv_001',
      user: {
        id: 'user_123',
        name: 'John Doe',
      },
      chatbot: {
        id: 'bot_001',
        name: 'Customer Support Bot',
      },
      status: 'completed',
      messageCount: 15,
      startTime: '2024-07-23 14:30:00',
      endTime: '2024-07-23 14:45:00',
      duration: '15m 30s',
      satisfaction: 5,
    },
    {
      key: '2',
      id: 'conv_002',
      user: {
        id: 'user_456',
        name: 'Jane Smith',
      },
      chatbot: {
        id: 'bot_002',
        name: 'Sales Assistant',
      },
      status: 'active',
      messageCount: 8,
      startTime: '2024-07-23 15:15:00',
      duration: '12m 45s',
    },
    {
      key: '3',
      id: 'conv_003',
      user: {
        id: 'user_789',
        name: 'Bob Johnson',
      },
      chatbot: {
        id: 'bot_001',
        name: 'Customer Support Bot',
      },
      status: 'abandoned',
      messageCount: 3,
      startTime: '2024-07-23 13:20:00',
      duration: '2m 15s',
    },
  ];

  // Mock messages for conversation details
  const mockMessages: Message[] = [
    {
      id: '1',
      sender: 'user',
      content: 'Hello, I need help with my account',
      timestamp: '2024-07-23 14:30:00',
    },
    {
      id: '2',
      sender: 'bot',
      content: 'Hello! I\'d be happy to help you with your account. Can you please tell me what specific issue you\'re experiencing?',
      timestamp: '2024-07-23 14:30:15',
    },
    {
      id: '3',
      sender: 'user',
      content: 'I can\'t log into my account. It says my password is incorrect.',
      timestamp: '2024-07-23 14:31:00',
    },
    {
      id: '4',
      sender: 'bot',
      content: 'I understand you\'re having trouble logging in. Let me help you reset your password. Please click on the "Forgot Password" link on the login page.',
      timestamp: '2024-07-23 14:31:30',
    },
  ];

  const columns: ColumnsType<ConversationData> = [
    {
      title: 'Conversation ID',
      dataIndex: 'id',
      key: 'id',
      width: 120,
    },
    {
      title: 'User',
      dataIndex: 'user',
      key: 'user',
      render: (user) => (
        <Space>
          <Avatar size="small" icon={<UserOutlined />} />
          <div>
            <div>{user.name}</div>
            <div style={{ fontSize: '12px', color: '#666' }}>ID: {user.id}</div>
          </div>
        </Space>
      ),
    },
    {
      title: 'Chatbot',
      dataIndex: 'chatbot',
      key: 'chatbot',
      render: (chatbot) => (
        <Space>
          <RobotOutlined style={{ color: '#1890ff' }} />
          <span>{chatbot.name}</span>
        </Space>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: 'active' | 'completed' | 'abandoned') => {
        const colorMap = {
          active: 'processing',
          completed: 'success',
          abandoned: 'error',
        } as const;
        return <Tag color={colorMap[status]}>{status.toUpperCase()}</Tag>;
      },
    },
    {
      title: 'Messages',
      dataIndex: 'messageCount',
      key: 'messageCount',
      sorter: (a, b) => a.messageCount - b.messageCount,
      render: (count) => (
        <Space>
          <MessageOutlined />
          {count}
        </Space>
      ),
    },
    {
      title: 'Duration',
      dataIndex: 'duration',
      key: 'duration',
      render: (duration) => (
        <Space>
          <ClockCircleOutlined />
          {duration}
        </Space>
      ),
    },
    {
      title: 'Start Time',
      dataIndex: 'startTime',
      key: 'startTime',
      sorter: (a, b) => new Date(a.startTime).getTime() - new Date(b.startTime).getTime(),
      render: (time) => dayjs(time).format('MMM DD, HH:mm'),
    },
    {
      title: 'Satisfaction',
      dataIndex: 'satisfaction',
      key: 'satisfaction',
      render: (rating) => rating ? `${rating}/5 ⭐` : 'N/A',
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Tooltip title="View Conversation">
          <Button 
            size="small" 
            icon={<EyeOutlined />} 
            onClick={() => handleViewConversation(record)}
          />
        </Tooltip>
      ),
    },
  ];

  const handleViewConversation = (conversation: ConversationData) => {
    setSelectedConversation(conversation);
    setIsModalVisible(true);
  };

  const handleModalClose = () => {
    setIsModalVisible(false);
    setSelectedConversation(null);
  };

  return (
    <div>
      <div style={{ marginBottom: 24, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <div>
          <Title level={2}>Conversations</Title>
          <div style={{ color: '#666' }}>Monitor and analyze all chatbot conversations</div>
        </div>
      </div>

      {/* Statistics Cards */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={6}>
          <Card>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#52c41a' }}>
                {data.filter(conv => conv.status === 'active').length}
              </div>
              <div>Active</div>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#1890ff' }}>
                {data.filter(conv => conv.status === 'completed').length}
              </div>
              <div>Completed</div>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#f5222d' }}>
                {data.filter(conv => conv.status === 'abandoned').length}
              </div>
              <div>Abandoned</div>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#722ed1' }}>
                {data.reduce((sum, conv) => sum + conv.messageCount, 0)}
              </div>
              <div>Total Messages</div>
            </div>
          </Card>
        </Col>
      </Row>

      {/* Filters */}
      <Card style={{ marginBottom: 16 }}>
        <Row gutter={[16, 16]} align="middle">
          <Col xs={24} sm={8}>
            <Search 
              placeholder="Search conversations..." 
              allowClear 
              prefix={<SearchOutlined />}
            />
          </Col>
          <Col xs={24} sm={6}>
            <Select placeholder="Filter by status" allowClear style={{ width: '100%' }}>
              <Option value="active">Active</Option>
              <Option value="completed">Completed</Option>
              <Option value="abandoned">Abandoned</Option>
            </Select>
          </Col>
          <Col xs={24} sm={6}>
            <Select placeholder="Filter by chatbot" allowClear style={{ width: '100%' }}>
              <Option value="bot_001">Customer Support Bot</Option>
              <Option value="bot_002">Sales Assistant</Option>
              <Option value="bot_003">FAQ Bot</Option>
            </Select>
          </Col>
          <Col xs={24} sm={4}>
            <Button icon={<FilterOutlined />} style={{ width: '100%' }}>
              More Filters
            </Button>
          </Col>
        </Row>
      </Card>

      {/* Table */}
      <Card>
        <Table 
          columns={columns} 
          dataSource={data}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} conversations`,
          }}
        />
      </Card>

      {/* Conversation Detail Modal */}
      <Modal
        title={selectedConversation ? `Conversation ${selectedConversation.id}` : 'Conversation Details'}
        open={isModalVisible}
        onCancel={handleModalClose}
        footer={null}
        width={800}
      >
        {selectedConversation && (
          <div>
            <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
              <Col span={8}>
                <div><strong>User:</strong> {selectedConversation.user.name}</div>
                <div><strong>Chatbot:</strong> {selectedConversation.chatbot.name}</div>
              </Col>
              <Col span={8}>
                <div><strong>Status:</strong> <Tag color={selectedConversation.status === 'completed' ? 'success' : 'processing'}>{selectedConversation.status}</Tag></div>
                <div><strong>Duration:</strong> {selectedConversation.duration}</div>
              </Col>
              <Col span={8}>
                <div><strong>Messages:</strong> {selectedConversation.messageCount}</div>
                <div><strong>Satisfaction:</strong> {selectedConversation.satisfaction ? `${selectedConversation.satisfaction}/5 ⭐` : 'N/A'}</div>
              </Col>
            </Row>

            <Divider>Messages</Divider>

            <div style={{ maxHeight: 400, overflowY: 'auto' }}>
              <List
                itemLayout="vertical"
                dataSource={mockMessages}
                renderItem={(message) => (
                  <List.Item
                    style={{
                      padding: '8px 0',
                      justifyContent: message.sender === 'user' ? 'flex-end' : 'flex-start'
                    }}
                  >
                    <div
                      style={{
                        maxWidth: '70%',
                        padding: '8px 12px',
                        borderRadius: '8px',
                        backgroundColor: message.sender === 'user' ? '#1890ff' : '#f0f0f0',
                        color: message.sender === 'user' ? 'white' : 'black',
                        marginLeft: message.sender === 'user' ? 'auto' : '0',
                        marginRight: message.sender === 'bot' ? 'auto' : '0',
                      }}
                    >
                      <div style={{ marginBottom: '4px' }}>
                        <Space>
                          {message.sender === 'user' ? <UserOutlined /> : <RobotOutlined />}
                          <span style={{ fontSize: '12px', opacity: 0.8 }}>
                            {dayjs(message.timestamp).format('HH:mm')}
                          </span>
                        </Space>
                      </div>
                      <div>{message.content}</div>
                    </div>
                  </List.Item>
                )}
              />
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default ConversationsPage;