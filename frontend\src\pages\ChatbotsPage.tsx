import React, { useState } from 'react';
import { 
  Typography, 
  Button, 
  Table, 
  Space, 
  Tag, 
  Modal, 
  Form, 
  Input, 
  Select, 
  Switch,
  Card,
  Row,
  Col,
  Tooltip
} from 'antd';
import { 
  PlusOutlined, 
  EditOutlined, 
  DeleteOutlined, 
  EyeOutlined,
  RobotOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';

const { Title } = Typography;
const { Option } = Select;

interface ChatbotData {
  key: string;
  id: string;
  name: string;
  type: string;
  status: 'active' | 'inactive' | 'maintenance';
  conversations: number;
  lastActive: string;
  createdAt: string;
}

const ChatbotsPage: React.FC = () => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingBot, setEditingBot] = useState<ChatbotData | null>(null);
  const [form] = Form.useForm();

  // Mock data
  const data: ChatbotData[] = [
    {
      key: '1',
      id: 'bot_001',
      name: 'Customer Support Bot',
      type: 'Customer Service',
      status: 'active',
      conversations: 1250,
      lastActive: '2 minutes ago',
      createdAt: '2024-01-15',
    },
    {
      key: '2',
      id: 'bot_002',
      name: 'Sales Assistant',
      type: 'Sales',
      status: 'active',
      conversations: 850,
      lastActive: '1 hour ago',
      createdAt: '2024-01-20',
    },
    {
      key: '3',
      id: 'bot_003',
      name: 'FAQ Bot',
      type: 'General',
      status: 'inactive',
      conversations: 320,
      lastActive: '1 day ago',
      createdAt: '2024-02-01',
    },
    {
      key: '4',
      id: 'bot_004',
      name: 'Technical Support',
      type: 'Technical',
      status: 'maintenance',
      conversations: 2100,
      lastActive: '3 hours ago',
      createdAt: '2024-01-10',
    },
  ];

  const columns: ColumnsType<ChatbotData> = [
    {
      title: 'Chatbot',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <Space>
          <RobotOutlined style={{ color: '#1890ff' }} />
          <div>
            <div style={{ fontWeight: 'bold' }}>{text}</div>
            <div style={{ fontSize: '12px', color: '#666' }}>ID: {record.id}</div>
          </div>
        </Space>
      ),
    },
    {
      title: 'Type',
      dataIndex: 'type',
      key: 'type',
      render: (type) => <Tag color="blue">{type}</Tag>,
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status) => {
        const color = status === 'active' ? 'green' : status === 'inactive' ? 'red' : 'orange';
        return <Tag color={color}>{status.toUpperCase()}</Tag>;
      },
    },
    {
      title: 'Conversations',
      dataIndex: 'conversations',
      key: 'conversations',
      sorter: (a, b) => a.conversations - b.conversations,
      render: (count) => count.toLocaleString(),
    },
    {
      title: 'Last Active',
      dataIndex: 'lastActive',
      key: 'lastActive',
    },
    {
      title: 'Created',
      dataIndex: 'createdAt',
      key: 'createdAt',
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="View Details">
            <Button size="small" icon={<EyeOutlined />} />
          </Tooltip>
          <Tooltip title="Edit">
            <Button 
              size="small" 
              icon={<EditOutlined />} 
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Tooltip title={record.status === 'active' ? 'Deactivate' : 'Activate'}>
            <Button 
              size="small" 
              icon={record.status === 'active' ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
              onClick={() => handleToggleStatus(record)}
            />
          </Tooltip>
          <Tooltip title="Delete">
            <Button 
              size="small" 
              danger 
              icon={<DeleteOutlined />}
              onClick={() => handleDelete(record)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  const handleAdd = () => {
    setEditingBot(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  const handleEdit = (bot: ChatbotData) => {
    setEditingBot(bot);
    form.setFieldsValue(bot);
    setIsModalVisible(true);
  };

  const handleDelete = (bot: ChatbotData) => {
    Modal.confirm({
      title: 'Delete Chatbot',
      content: `Are you sure you want to delete "${bot.name}"? This action cannot be undone.`,
      okText: 'Delete',
      okType: 'danger',
      cancelText: 'Cancel',
      onOk() {
        console.log('Deleting bot:', bot.id);
      },
    });
  };

  const handleToggleStatus = (bot: ChatbotData) => {
    console.log('Toggling status for bot:', bot.id);
  };

  const handleModalOk = () => {
    form.validateFields().then(() => {
      console.log('Form values:', form.getFieldsValue());
      setIsModalVisible(false);
      form.resetFields();
    });
  };

  const handleModalCancel = () => {
    setIsModalVisible(false);
    form.resetFields();
  };

  return (
    <div>
      <div style={{ marginBottom: 24, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <div>
          <Title level={2}>Chatbots Management</Title>
          <div style={{ color: '#666' }}>Manage your chatbots and monitor their performance</div>
        </div>
        <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
          Create Chatbot
        </Button>
      </div>

      {/* Statistics Cards */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={8}>
          <Card>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#52c41a' }}>
                {data.filter(bot => bot.status === 'active').length}
              </div>
              <div>Active Bots</div>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#1890ff' }}>
                {data.reduce((sum, bot) => sum + bot.conversations, 0).toLocaleString()}
              </div>
              <div>Total Conversations</div>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#722ed1' }}>
                {data.length}
              </div>
              <div>Total Bots</div>
            </div>
          </Card>
        </Col>
      </Row>

      {/* Table */}
      <Card>
        <Table 
          columns={columns} 
          dataSource={data}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`,
          }}
        />
      </Card>

      {/* Create/Edit Modal */}
      <Modal
        title={editingBot ? 'Edit Chatbot' : 'Create New Chatbot'}
        open={isModalVisible}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
        width={600}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="name"
            label="Chatbot Name"
            rules={[{ required: true, message: 'Please enter chatbot name' }]}
          >
            <Input placeholder="Enter chatbot name" />
          </Form.Item>

          <Form.Item
            name="type"
            label="Type"
            rules={[{ required: true, message: 'Please select chatbot type' }]}
          >
            <Select placeholder="Select chatbot type">
              <Option value="Customer Service">Customer Service</Option>
              <Option value="Sales">Sales</Option>
              <Option value="Technical">Technical</Option>
              <Option value="General">General</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="description"
            label="Description"
          >
            <Input.TextArea rows={3} placeholder="Enter chatbot description" />
          </Form.Item>

          <Form.Item
            name="status"
            label="Status"
            valuePropName="checked"
          >
            <Switch checkedChildren="Active" unCheckedChildren="Inactive" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ChatbotsPage;